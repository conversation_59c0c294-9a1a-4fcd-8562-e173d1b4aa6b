# Docker Compose for Development Environment

services:
  # Ollama service (same as production)
  ollama:
    image: ollama/ollama:latest
    container_name: vocabulary-ollama-dev
    ports:
      - "11434:11434"
    volumes:
      - ollama_data_dev:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    networks:
      - vocabulary-network-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # FastAPI Backend with hot reload
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: vocabulary-backend-dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv  # Exclude venv from volume mount
    environment:
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - OLLAMA_MODEL=deepseek-r1:latest
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    # Using host Ollama service - no dependency needed
    networks:
      - vocabulary-network-dev
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 6G
        reservations:
          memory: 2G
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # React Frontend with hot reload
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: vocabulary-frontend-dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Exclude node_modules from volume mount
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true  # Enable hot reload in Docker
    depends_on:
      - backend
    networks:
      - vocabulary-network-dev
    restart: unless-stopped
    command: ["npm", "start"]

  # Model initialization service
  model-init:
    image: ollama/ollama:latest
    container_name: vocabulary-model-init-dev
    volumes:
      - ollama_data_dev:/root/.ollama
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - vocabulary-network-dev
    entrypoint: ["/bin/sh", "-c"]
    command: >
      "echo 'Waiting for Ollama to be ready...' &&
       sleep 10 &&
       echo 'Pulling deepseek-r1:latest model...' &&
       OLLAMA_HOST=http://ollama:11434 ollama pull deepseek-r1:latest &&
       echo 'Model initialization complete!'"
    restart: "no"

volumes:
  ollama_data_dev:
    driver: local

networks:
  vocabulary-network-dev:
    driver: bridge
