# Docker Compose for Production Environment

services:
  # Ollama service
  ollama:
    image: ollama/ollama:latest
    container_name: vocabulary-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    networks:
      - vocabulary-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: vocabulary-backend
    ports:
      - "8000:8000"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - OLLAMA_MODEL=llama3.2:3b
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - vocabulary-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vocabulary-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - vocabulary-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Model initialization service
  model-init:
    image: ollama/ollama:latest
    container_name: vocabulary-model-init
    volumes:
      - ollama_data:/root/.ollama
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - vocabulary-network
    entrypoint: ["/bin/sh", "-c"]
    command: >
      "echo 'Waiting for Ollama to be ready...' &&
       sleep 10 &&
       echo 'Pulling llama3.2:3b model...' &&
       OLLAMA_HOST=http://ollama:11434 ollama pull llama3.2:3b &&
       echo 'Model initialization complete!'"
    restart: "no"

volumes:
  ollama_data:
    driver: local

networks:
  vocabulary-network:
    driver: bridge
