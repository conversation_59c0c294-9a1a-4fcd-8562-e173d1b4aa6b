from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import os
from dotenv import load_dotenv
from services.vocabulary_service import VocabularyService

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Vocabulary Learning API",
    description="API for vocabulary learning with word meanings, imagery, and examples",
    version="1.0.0"
)

# Configure CORS
origins = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize vocabulary service
vocabulary_service = VocabularyService()

class WordRequest(BaseModel):
    word: str

class VocabularyResponse(BaseModel):
    word: str
    meaning: str
    visual_description: str
    synonyms: list[str]
    examples: list[str]
    success: bool
    error: Optional[str] = None

@app.get("/")
async def root():
    return {"message": "Vocabulary Learning API", "status": "running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test Ollama connection
        is_healthy = await vocabulary_service.health_check()
        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "ollama_connected": is_healthy
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "ollama_connected": False
        }

@app.post("/api/vocabulary/analyze", response_model=VocabularyResponse)
async def analyze_word(request: WordRequest):
    """
    Analyze a word and return its meaning, visual description, and usage examples
    """
    try:
        if not request.word or not request.word.strip():
            raise HTTPException(status_code=400, detail="Word cannot be empty")
        
        word = request.word.strip().lower()
        
        # Get vocabulary analysis from the service
        result = await vocabulary_service.analyze_word(word)
        
        return VocabularyResponse(
            word=word,
            meaning=result["meaning"],
            visual_description=result["visual_description"],
            synonyms=result["synonyms"],
            examples=result["examples"],
            success=True
        )
        
    except Exception as e:
        return VocabularyResponse(
            word=request.word,
            meaning="",
            visual_description="",
            synonyms=[],
            examples=[],
            success=False,
            error=str(e)
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
