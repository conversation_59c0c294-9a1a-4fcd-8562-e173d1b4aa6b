.vocabulary-search {
  margin-bottom: 2rem;
}

.search-form {
  display: flex;
  justify-content: center;
}

.input-group {
  display: flex;
  gap: 0.75rem;
  max-width: 600px;
  width: 100%;
}

.word-input {
  flex: 1;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1e293b;
  outline: none;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.word-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.word-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.search-button {
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-button:hover {
  background: #2563eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.search-button:active {
  transform: translateY(0);
}

.search-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
    gap: 1rem;
  }

  .word-input, .search-button {
    width: 100%;
  }

  .search-button {
    justify-content: center;
  }
}
