#!/bin/bash

# Vocabulary Learning App - Docker Test Script

echo "🧪 Testing Vocabulary Learning App Docker deployment..."

# Check if services are running
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "🔍 Testing service endpoints..."

# Test Ollama
echo "1. Testing Ollama service..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "   ✅ Ollama is accessible"
else
    echo "   ❌ Ollama is not accessible"
fi

# Test Backend Health
echo "2. Testing Backend health..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "   ✅ Backend is healthy"
    
    # Test Backend API
    echo "3. Testing Backend API..."
    response=$(curl -s -X POST "http://localhost:8000/api/vocabulary/analyze" \
        -H "Content-Type: application/json" \
        -d '{"word": "test"}')
    
    if echo "$response" | grep -q '"success":true'; then
        echo "   ✅ Backend API is working"
    else
        echo "   ❌ Backend API failed"
        echo "   Response: $response"
    fi
else
    echo "   ❌ Backend is not healthy"
fi

# Test Frontend
echo "4. Testing Frontend..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "   ✅ Frontend is accessible"
else
    echo "   ❌ Frontend is not accessible"
fi

echo ""
echo "📋 Service URLs:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:8000"
echo "   API Docs:  http://localhost:8000/docs"
echo "   Ollama:    http://localhost:11434"

echo ""
echo "🔍 Container Resource Usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""
echo "💾 Volume Usage:"
docker system df -v | grep vocabulary

echo ""
echo "🧪 Test complete!"
