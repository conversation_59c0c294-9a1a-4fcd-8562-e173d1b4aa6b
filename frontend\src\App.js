import React, { useState } from 'react';
import './App.css';
import VocabularySearch from './components/VocabularySearch';
import VocabularyDisplay from './components/VocabularyDisplay';
import Header from './components/Header';

function App() {
  const [vocabularyData, setVocabularyData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchWord, setSearchWord] = useState('');

  const handleWordAnalysis = (data) => {
    setVocabularyData(data);
  };

  const handleLoading = (isLoading) => {
    setLoading(isLoading);
  };

  const handleError = (errorMessage) => {
    setError(errorMessage);
  };

  const handleSynonymClick = (synonym) => {
    setSearchWord(synonym);
    setError(null);
    setVocabularyData(null);
  };

  return (
    <div className="App">
      <Header />
      <main className="main-content">
        <VocabularySearch
          onWordAnalysis={handleWordAnalysis}
          onLoading={handleLoading}
          onError={handleError}
          searchWord={searchWord}
          onSearchWordChange={setSearchWord}
        />
        {error && (
          <div className="error-message">
            <p>Error: {error}</p>
          </div>
        )}
        {loading && (
          <div className="loading-message">
            <p>Analyzing word...</p>
            <div className="spinner"></div>
          </div>
        )}
        {vocabularyData && !loading && (
          <VocabularyDisplay
            data={vocabularyData}
            onSynonymClick={handleSynonymClick}
          />
        )}
      </main>
    </div>
  );
}

export default App;
