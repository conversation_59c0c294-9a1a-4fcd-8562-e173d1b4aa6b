import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000, // 30 seconds timeout for LLM responses
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout - the server took too long to respond');
    }
    
    if (error.response?.status === 500) {
      throw new Error('Server error - please try again later');
    }
    
    if (error.response?.status === 404) {
      throw new Error('API endpoint not found - please check if the backend is running');
    }
    
    if (!error.response) {
      throw new Error('Network error - please check if the backend is running');
    }
    
    throw error;
  }
);

/**
 * Analyze a word using the vocabulary API
 * @param {string} word - The word to analyze
 * @returns {Promise<Object>} - The vocabulary analysis result
 */
export const analyzeWord = async (word) => {
  try {
    const response = await api.post('/api/vocabulary/analyze', {
      word: word.trim()
    });
    
    return response.data;
  } catch (error) {
    console.error('Error analyzing word:', error);
    throw error;
  }
};

/**
 * Check the health of the API and Ollama connection
 * @returns {Promise<Object>} - Health status
 */
export const checkHealth = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    console.error('Error checking health:', error);
    throw error;
  }
};

export default api;
