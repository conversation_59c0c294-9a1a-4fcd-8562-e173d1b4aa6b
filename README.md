# 📚 Vocabulary Learning App

A modern vocabulary learning application that provides comprehensive word analysis including meanings, visual imagery descriptions, and usage examples using Ollama LLM.

## ✨ Features

- **Word Analysis**: Get detailed meanings and definitions
- **Visual Memory**: Memorable visual descriptions to help retain words
- **Synonyms**: Related words with similar meanings for vocabulary expansion
- **Usage Examples**: Real-world examples showing words in context
- **Modern UI**: Clean, responsive React interface
- **Fast API**: High-performance FastAPI backend
- **AI-Powered**: Uses Ollama LLM for intelligent word analysis

## 🛠️ Prerequisites

### For Docker Setup (Recommended)
- **Docker** - Latest version
- **Docker Compose** - Latest version

### For Manual Setup
- **Python 3.8+** - For the FastAPI backend
- **Node.js 16+** - For the React frontend
- **Ollama** - For LLM functionality

## 🚀 Quick Start

### Option 1: Docker Setup (Recommended) 🐳

1. **Clone the repository and navigate to the project directory**

2. **Start with Docker**:
   ```bash
   ./docker-start.sh
   ```

3. **Wait for all services to start** (first run may take 5-10 minutes to download models)

4. **Open your browser** to [http://localhost:3000](http://localhost:3000)

5. **Stop services**:
   ```bash
   ./docker-stop.sh
   ```

### Option 2: Manual Setup

1. **Install Ollama** from [https://ollama.ai](https://ollama.ai)

2. **Start Ollama and pull a model**:
   ```bash
   ollama serve
   ollama pull llama3.2:3b
   ```

3. **Run the startup script**:
   ```bash
   ./start.sh
   ```

4. **Open your browser** to [http://localhost:3000](http://localhost:3000)

### Option 2: Manual Setup

#### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create and activate virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env if needed
   ```

5. Start the FastAPI server:
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

#### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env if needed
   ```

4. Start the React development server:
   ```bash
   npm start
   ```

## 🎯 Usage

1. **Enter a word** in the search box
2. **Click "Analyze"** or press Enter
3. **View the results**:
   - 📖 **Meaning**: Clear definition of the word
   - 🎨 **Visual Memory**: Imagery to help remember the word
   - ✨ **Synonyms**: Related words with similar meanings
   - 💡 **Usage Examples**: Sentences showing the word in context

## 🔧 API Endpoints

### Health Check
- `GET /health` - Check API and Ollama connection status

### Vocabulary Analysis
- `POST /api/vocabulary/analyze` - Analyze a word
  ```json
  {
    "word": "serendipity"
  }
  ```

### API Documentation
- Visit [http://localhost:8000/docs](http://localhost:8000/docs) for interactive API documentation

## 🏗️ Project Structure

```
vocabulary-app/
├── backend/                 # FastAPI backend
│   ├── main.py             # Main application file
│   ├── services/           # Business logic
│   │   └── vocabulary_service.py
│   ├── requirements.txt    # Python dependencies
│   └── .env.example       # Environment template
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API integration
│   │   └── App.js         # Main app component
│   ├── package.json       # Node.js dependencies
│   └── .env.example       # Environment template
├── start.sh               # Automated startup script
└── README.md             # This file
```

## 🐳 Docker Deployment

### Architecture
The Docker setup includes:
- **Ollama Container**: Runs the LLM service with persistent model storage
- **FastAPI Backend**: Python API service
- **React Frontend**: Nginx-served React application
- **Automatic Model Download**: Downloads llama3.2:3b on first run

### Docker Commands

```bash
# Start all services
./docker-start.sh

# Stop all services
./docker-stop.sh

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f ollama

# Restart a specific service
docker-compose restart backend

# Rebuild and restart
docker-compose up --build -d
```

### Docker Volumes
- `ollama_data`: Stores downloaded models (persistent across restarts)

### Docker Network
All services communicate through the `vocabulary-network` bridge network.

## 🔧 Configuration

### Backend Configuration (.env)
```env
# Local development
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:3b
CORS_ORIGINS=http://localhost:3000

# Docker environment (handled automatically)
# OLLAMA_BASE_URL=http://ollama:11434
# CORS_ORIGINS=http://localhost:3000,http://frontend:3000
```

### Frontend Configuration (.env)
```env
# Local development
REACT_APP_API_URL=http://localhost:8000

# Docker environment (handled automatically)
# REACT_APP_API_URL=http://localhost:8000
```

## 🧪 Testing

### Test the Backend
```bash
cd backend
curl -X POST "http://localhost:8000/api/vocabulary/analyze" \
     -H "Content-Type: application/json" \
     -d '{"word": "example"}'
```

### Test Ollama Connection
```bash
curl http://localhost:11434/api/tags
```

## 🐛 Troubleshooting

### Common Issues

1. **Ollama not running**
   ```bash
   ollama serve
   ```

2. **Model not available**
   ```bash
   ollama pull llama2
   ```

3. **Port conflicts**
   - Backend: Change port in `uvicorn` command
   - Frontend: Set `PORT=3001` in frontend/.env

4. **CORS issues**
   - Update `CORS_ORIGINS` in backend/.env

### Logs and Debugging

- **Backend logs**: Check terminal running uvicorn
- **Frontend logs**: Check browser console
- **Ollama logs**: Check Ollama service logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.
