import React from 'react';
import './VocabularyDisplay.css';

const VocabularyDisplay = ({ data, onSynonymClick }) => {
  if (!data) return null;

  const handleSynonymClick = (synonym) => {
    if (onSynonymClick) {
      onSynonymClick(synonym);
    }
  };

  return (
    <div className="vocabulary-display">
      <div className="word-header">
        <h2 className="word-title">{data.word}</h2>
      </div>
      
      <div className="content-grid">
        <div className="content-card meaning-card">
          <div className="card-header">
            <h3>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Definition
            </h3>
          </div>
          <div className="card-content">
            <p>{data.meaning}</p>
          </div>
        </div>

        <div className="content-card visual-card">
          <div className="card-header">
            <h3>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" stroke="currentColor" strokeWidth="2"/>
                <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" stroke="currentColor" strokeWidth="2"/>
              </svg>
              Visual Memory
            </h3>
          </div>
          <div className="card-content">
            <p>{data.visual_description}</p>
          </div>
        </div>

        <div className="content-card synonyms-card">
          <div className="card-header">
            <h3>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Synonyms
            </h3>
          </div>
          <div className="card-content">
            <div className="synonyms-list">
              {data.synonyms.map((synonym, index) => (
                <span
                  key={index}
                  className="synonym-tag clickable"
                  onClick={() => handleSynonymClick(synonym)}
                  title={`Click to analyze "${synonym}"`}
                  style={{
                    animationDelay: `${index * 0.1}s`
                  }}
                >
                  {synonym}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="content-card examples-card">
          <div className="card-header">
            <h3>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              Usage Examples
            </h3>
          </div>
          <div className="card-content">
            <ul className="examples-list">
              {data.examples.map((example, index) => (
                <li key={index} className="example-item">
                  {example}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VocabularyDisplay;
