#!/bin/bash

# Vocabulary Learning App Startup Script

echo "🚀 Starting Vocabulary Learning App..."

# Check if Ollama is running
echo "📡 Checking Ollama connection..."
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ Ollama is not running. Please start Ollama first:"
    echo "   ollama serve"
    echo "   ollama pull llama2"
    exit 1
fi

echo "✅ Ollama is running"

# Function to start backend
start_backend() {
    echo "🐍 Starting FastAPI backend..."
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "📦 Creating virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Copy .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "📝 Created .env file from .env.example"
    fi
    
    # Start FastAPI server
    echo "🚀 Starting FastAPI server on http://localhost:8000"
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
}

# Function to start frontend
start_frontend() {
    echo "⚛️  Starting React frontend..."
    cd frontend
    
    # Copy .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "📝 Created .env file from .env.example"
    fi
    
    # Install dependencies
    echo "📦 Installing Node.js dependencies..."
    npm install
    
    # Start React development server
    echo "🚀 Starting React server on http://localhost:3000"
    npm start &
    FRONTEND_PID=$!
    cd ..
}

# Start both services
start_backend
sleep 3
start_frontend

echo ""
echo "🎉 Application started successfully!"
echo "📖 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
trap 'echo "🛑 Stopping services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
