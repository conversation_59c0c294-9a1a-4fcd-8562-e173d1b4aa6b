import os
import json
import re
from typing import Dict, List
import ollama
from ollama import AsyncClient

class VocabularyService:
    def __init__(self):
        self.base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model = os.getenv("OLLAMA_MODEL", "llama2")
        self.client = AsyncClient(host=self.base_url)
    
    async def health_check(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            response = await self.client.list()
            return True
        except Exception:
            return False
    
    async def analyze_word(self, word: str) -> Dict[str, any]:
        """
        Analyze a word using Ollama LLM to get meaning, visual description, and examples
        """
        try:
            # Create a comprehensive prompt for word analysis
            prompt = self._create_analysis_prompt(word)
            
            # Get response from Ollama
            response = await self.client.chat(
                model=self.model,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )

            # Parse the response
            content = response['message']['content']
            parsed_result = self._parse_response(content, word)
            
            return parsed_result
            
        except Exception as e:
            # Return fallback response if Ollama fails
            return self._get_fallback_response(word, str(e))
    
    def _create_analysis_prompt(self, word: str) -> str:
        """Create a structured prompt for word analysis"""
        return f"""
Analyze the word "{word}" and provide EXACTLY the following format:

MEANING: [Write a clear, concise definition in 1-2 sentences]

VISUAL_DESCRIPTION: [Create a vivid, memorable visual scene or image that helps remember this word in 2-3 sentences]

SYNONYMS: [List 8-12 words that have similar or the same meaning as this word, separated by commas]

EXAMPLES:
- [First example sentence using the word]
- [Second example sentence using the word]
- [Third example sentence using the word]

IMPORTANT:
- Use EXACTLY the section headers shown above
- Keep each section separate and distinct
- Do not mix content between sections
- Each example should be a complete sentence
- Synonyms should be single words or short phrases, separated by commas

Word to analyze: {word}
"""
    
    def _parse_response(self, content: str, word: str) -> Dict[str, any]:
        """Parse the LLM response into structured data"""
        try:
            # Initialize default values
            meaning = ""
            visual_description = ""
            synonyms = []
            examples = []

            # First, try to extract from structured format (handle both : and ** formats)
            meaning_match = re.search(r'(?:\*\*)?MEANING(?:\*\*)?:?\s*\n?\s*(.+?)(?=\n\s*(?:\*\*)?VISUAL[_ ]DESCRIPTION|$)', content, re.DOTALL | re.IGNORECASE)
            visual_match = re.search(r'(?:\*\*)?VISUAL[_ ]DESCRIPTION(?:\*\*)?:?\s*\n?\s*(.+?)(?=\n\s*(?:\*\*)?SYNONYMS|$)', content, re.DOTALL | re.IGNORECASE)
            synonyms_match = re.search(r'(?:\*\*)?SYNONYMS(?:\*\*)?:?\s*\n?\s*(.+?)(?=\n\s*(?:\*\*)?EXAMPLES|$)', content, re.DOTALL | re.IGNORECASE)
            examples_match = re.search(r'(?:\*\*)?EXAMPLES(?:\*\*)?:?\s*\n?\s*(.+?)$', content, re.DOTALL | re.IGNORECASE)

            if meaning_match:
                meaning = meaning_match.group(1).strip()

            if visual_match:
                visual_description = visual_match.group(1).strip()

            if synonyms_match:
                synonyms_text = synonyms_match.group(1).strip()
                # Split by commas and clean up
                synonyms = [syn.strip() for syn in synonyms_text.split(',') if syn.strip()]
                # Limit to 12 synonyms and filter out very short ones
                synonyms = [syn for syn in synonyms if len(syn) > 1][:12]

            if examples_match:
                examples_text = examples_match.group(1).strip()
                # Split by lines and clean up
                example_lines = [line.strip() for line in examples_text.split('\n') if line.strip()]
                examples = []
                for line in example_lines:
                    # Remove bullet points and clean up
                    clean_line = re.sub(r'^[-*•]\s*', '', line).strip()
                    if clean_line and len(clean_line) > 10:  # Filter out very short lines
                        examples.append(clean_line)

            # If structured parsing failed, try to parse the unstructured response
            if not meaning and not visual_description and not examples:
                # Split content into paragraphs
                paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

                if len(paragraphs) >= 1:
                    # First paragraph is likely the meaning
                    meaning = paragraphs[0]

                    # Look for visual description keywords
                    for para in paragraphs:
                        if any(keyword in para.lower() for keyword in ['imagine', 'picture', 'visualize', 'visual', 'see']):
                            visual_description = para
                            break

                    # Look for examples (sentences with the word)
                    for para in paragraphs:
                        if word.lower() in para.lower() and len(para.split('.')) > 1:
                            # Split into sentences and filter those containing the word
                            sentences = [s.strip() for s in para.split('.') if s.strip()]
                            for sentence in sentences:
                                if word.lower() in sentence.lower() and len(sentence) > 20:
                                    examples.append(sentence + '.')
                                    if len(examples) >= 3:
                                        break
                            if examples:
                                break

            # Clean up the extracted content
            if meaning:
                # Remove any remaining section headers from meaning
                meaning = re.sub(r'(VISUAL_DESCRIPTION:|EXAMPLES:).*$', '', meaning, flags=re.DOTALL | re.IGNORECASE).strip()

            # Fallback if parsing failed
            if not meaning:
                meaning = f"Definition for '{word}' - please check the spelling or try another word."
            if not visual_description:
                visual_description = f"Imagine the concept of '{word}' in your mind."
            if not synonyms:
                synonyms = ["similar", "related", "equivalent", "comparable", "alike", "corresponding", "matching", "parallel", "analogous", "identical"]
            if not examples:
                examples = [f"Here is an example with {word}.", f"Another way to use {word}.", f"A third example of {word}."]

            return {
                "meaning": meaning,
                "visual_description": visual_description,
                "synonyms": synonyms[:12],  # Limit to 12 synonyms
                "examples": examples[:3]  # Limit to 3 examples
            }
            
        except Exception as e:
            return self._get_fallback_response(word, f"Parsing error: {str(e)}")
    
    def _get_fallback_response(self, word: str, error: str) -> Dict[str, any]:
        """Provide a fallback response when Ollama is unavailable"""
        return {
            "meaning": f"Unable to fetch definition for '{word}'. Please ensure Ollama is running.",
            "visual_description": f"Imagine the word '{word}' and what it might represent visually.",
            "synonyms": ["similar", "related", "equivalent", "comparable", "alike", "corresponding", "matching", "parallel", "analogous", "identical"],
            "examples": [
                f"Example sentence with {word}.",
                f"Another context for {word}.",
                f"A third usage of {word}."
            ]
        }
