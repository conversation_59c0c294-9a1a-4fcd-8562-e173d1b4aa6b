#!/bin/bash

# Vocabulary Learning App - Docker Startup Script

set -e

echo "🐳 Starting Vocabulary Learning App with Docker..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

echo "✅ Docker is running"

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up..."
    docker-compose down
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up --build -d

echo "⏳ Waiting for services to be healthy..."

# Wait for Ollama to be ready
echo "📡 Waiting for Ollama service..."
timeout=300
counter=0
while ! docker-compose exec -T ollama curl -f http://localhost:11434/api/tags > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for Ollama service"
        exit 1
    fi
    echo "   Ollama not ready yet... (${counter}s/${timeout}s)"
    sleep 5
    counter=$((counter + 5))
done
echo "✅ Ollama service is ready"

# Wait for backend to be ready
echo "🐍 Waiting for Backend service..."
timeout=120
counter=0
while ! docker-compose exec -T backend curl -f http://localhost:8000/health > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for Backend service"
        exit 1
    fi
    echo "   Backend not ready yet... (${counter}s/${timeout}s)"
    sleep 5
    counter=$((counter + 5))
done
echo "✅ Backend service is ready"

# Wait for frontend to be ready
echo "⚛️  Waiting for Frontend service..."
timeout=120
counter=0
while ! curl -f http://localhost:3000 > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for Frontend service"
        exit 1
    fi
    echo "   Frontend not ready yet... (${counter}s/${timeout}s)"
    sleep 5
    counter=$((counter + 5))
done
echo "✅ Frontend service is ready"

echo ""
echo "🎉 All services are running successfully!"
echo ""
echo "📖 Application URLs:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:8000"
echo "   API Docs:  http://localhost:8000/docs"
echo "   Ollama:    http://localhost:11434"
echo ""
echo "📊 Service Status:"
docker-compose ps
echo ""
echo "📝 To view logs:"
echo "   docker-compose logs -f"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose down"
echo ""
echo "Press Ctrl+C to stop all services and exit"

# Keep script running and show logs
docker-compose logs -f
