import asyncio
import os
from ollama import AsyncClient

async def test_ollama():
    try:
        base_url = os.getenv("OLLAMA_BASE_URL", "http://host.docker.internal:11434")
        model = os.getenv("OLLAMA_MODEL", "gemma2:2b")
        
        print(f"Testing connection to: {base_url}")
        print(f"Using model: {model}")
        
        client = AsyncClient(host=base_url)
        
        # Test list models
        print("Testing list models...")
        models = await client.list()
        print(f"Available models: {models}")
        
        # Test chat
        print("Testing chat...")
        response = await client.chat(
            model=model,
            messages=[
                {
                    'role': 'user',
                    'content': 'Define the word "test" in one sentence.'
                }
            ]
        )
        print(f"Chat response: {response}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ollama())
