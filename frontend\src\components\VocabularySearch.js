import React, { useState, useEffect } from 'react';
import './VocabularySearch.css';
import { analyzeWord } from '../services/api';

const VocabularySearch = ({ onWordAnalysis, onLoading, onError, searchWord, onSearchWordChange }) => {
  const [word, setWord] = useState('');

  // Handle external word updates (from synonym clicks)
  useEffect(() => {
    if (searchWord && searchWord !== word) {
      setWord(searchWord);
      // Auto-trigger analysis for synonym clicks
      analyzeWordAsync(searchWord);
      // Clear the external search word
      onSearchWordChange('');
    }
  }, [searchWord, word, onSearchWordChange]);

  const analyzeWordAsync = async (wordToAnalyze) => {
    if (!wordToAnalyze.trim()) {
      onError('Please enter a word to analyze');
      return;
    }

    onError(null);
    onLoading(true);

    try {
      const result = await analyzeWord(wordToAnalyze.trim());

      if (result.success) {
        onWordAnalysis(result);
      } else {
        onError(result.error || 'Failed to analyze word');
      }
    } catch (error) {
      onError('Failed to connect to the server. Please check if the backend is running.');
    } finally {
      onLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    await analyzeWordAsync(word);
  };

  const handleInputChange = (e) => {
    setWord(e.target.value);
    if (onError) {
      onError(null); // Clear any existing errors when user types
    }
  };

  return (
    <div className="vocabulary-search">
      <form onSubmit={handleSubmit} className="search-form">
        <div className="input-group">
          <input
            type="text"
            value={word}
            onChange={handleInputChange}
            placeholder="Enter a word to analyze..."
            className="word-input"
            autoFocus
          />
          <button type="submit" className="search-button">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Analyze
          </button>
        </div>
      </form>
    </div>
  );
};

export default VocabularySearch;
