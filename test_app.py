#!/usr/bin/env python3
"""
Simple test script to verify the vocabulary app is working correctly.
"""

import requests
import json
import time
import sys

def test_backend_health():
    """Test if the backend is running and healthy"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend health: {data['status']}")
            print(f"✅ Ollama connected: {data['ollama_connected']}")
            return data['ollama_connected']
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend not reachable: {e}")
        return False

def test_vocabulary_analysis():
    """Test the vocabulary analysis endpoint"""
    test_word = "serendipity"
    
    try:
        response = requests.post(
            "http://localhost:8000/api/vocabulary/analyze",
            json={"word": test_word},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Word analysis successful for '{test_word}'")
            print(f"   Success: {data['success']}")
            print(f"   Meaning: {data['meaning'][:100]}...")
            print(f"   Visual: {data['visual_description'][:100]}...")
            print(f"   Examples: {len(data['examples'])} provided")
            return True
        else:
            print(f"❌ Word analysis failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Word analysis request failed: {e}")
        return False

def test_frontend():
    """Test if the frontend is accessible"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend returned status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend not reachable: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Vocabulary Learning App")
    print("=" * 40)
    
    # Test backend health
    print("\n1. Testing Backend Health...")
    backend_healthy = test_backend_health()
    
    if not backend_healthy:
        print("\n❌ Backend is not healthy. Please check:")
        print("   - Is the FastAPI server running?")
        print("   - Is Ollama running and accessible?")
        sys.exit(1)
    
    # Test vocabulary analysis
    print("\n2. Testing Vocabulary Analysis...")
    analysis_works = test_vocabulary_analysis()
    
    if not analysis_works:
        print("\n❌ Vocabulary analysis failed. Please check the logs.")
        sys.exit(1)
    
    # Test frontend
    print("\n3. Testing Frontend...")
    frontend_works = test_frontend()
    
    if not frontend_works:
        print("\n❌ Frontend is not accessible. Please check if React is running.")
        sys.exit(1)
    
    print("\n🎉 All tests passed! The application is working correctly.")
    print("\n📖 Open http://localhost:3000 to use the app")

if __name__ == "__main__":
    main()
