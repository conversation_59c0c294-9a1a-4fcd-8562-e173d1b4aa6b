.vocabulary-display {
  max-width: 1000px;
  margin: 0 auto;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.word-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.word-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-transform: capitalize;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.content-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  font-size: 1.125rem;
  margin: 0;
  color: #334155;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  color: #475569;
  line-height: 1.7;
}

.card-content p {
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
  text-align: left;
  letter-spacing: 0.01em;
}

.examples-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.example-item {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  color: #475569;
  line-height: 1.7;
}

.example-item:hover {
  background: #f1f5f9;
  border-left-color: #2563eb;
  transform: translateX(4px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Synonyms styling */
.synonyms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.synonym-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: default;
  display: inline-block;
  animation: fadeInUp 0.6s ease-out both;
}

.synonym-tag.clickable {
  cursor: pointer;
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.synonym-tag.clickable:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.synonym-tag.clickable:active {
  transform: translateY(0);
}

/* Minimalist card types with subtle accents */
.meaning-card {
  border-left: 4px solid #10b981;
}

.meaning-card .card-header h3 {
  color: #059669;
}

.meaning-card .card-header h3 svg {
  color: #10b981;
}

.visual-card {
  border-left: 4px solid #8b5cf6;
}

.visual-card .card-header h3 {
  color: #7c3aed;
}

.visual-card .card-header h3 svg {
  color: #8b5cf6;
}

.synonyms-card {
  border-left: 4px solid #3b82f6;
}

.synonyms-card .card-header h3 {
  color: #2563eb;
}

.synonyms-card .card-header h3 svg {
  color: #3b82f6;
}

.examples-card {
  border-left: 4px solid #f59e0b;
}

.examples-card .card-header h3 {
  color: #d97706;
}

.examples-card .card-header h3 svg {
  color: #f59e0b;
}

/* Responsive design */
@media (min-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .examples-card {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
  }

  .examples-card {
    grid-column: 1 / -1;
  }
}

@media (max-width: 767px) {
  .word-title {
    font-size: 2rem;
  }

  .content-card {
    padding: 1.25rem;
  }

  .card-header h3 {
    font-size: 1rem;
  }

  .card-content p {
    font-size: 0.95rem;
  }

  .vocabulary-display {
    margin-bottom: 2rem;
  }
}
