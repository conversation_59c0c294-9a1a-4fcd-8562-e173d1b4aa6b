#!/bin/bash

# Vocabulary Learning App - Docker Stop Script

echo "🛑 Stopping Vocabulary Learning App Docker services..."

# Stop and remove containers
docker-compose down

echo "🧹 Cleaning up unused Docker resources..."

# Remove unused images (optional)
read -p "Do you want to remove unused Docker images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker image prune -f
    echo "✅ Unused images removed"
fi

# Remove unused volumes (optional - WARNING: This will remove the Ollama model data)
read -p "Do you want to remove Docker volumes? This will delete downloaded models! (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose down -v
    docker volume prune -f
    echo "⚠️  Volumes removed - you'll need to download models again"
fi

echo "✅ Docker services stopped successfully"
