.App {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  letter-spacing: 0.01em;
}

.error-message {
  background: white;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  color: #dc2626;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-message {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #64748b;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .error-message,
  .loading-message {
    margin: 1rem 0;
    padding: 1rem;
  }
}
