#!/bin/bash

# Vocabulary Learning App - Docker Development Startup Script

set -e

echo "🐳 Starting Vocabulary Learning App with <PERSON><PERSON> (Development Mode)..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if host <PERSON><PERSON><PERSON> is running
echo "📡 Checking host Ollama service..."
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ Host Ollama is not running. Please start Ollama first:"
    echo "   ollama serve"
    echo "   ollama pull deepseek-r1:latest"
    exit 1
fi

echo "✅ Host Ollama is running"

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up..."
    docker-compose -f docker-compose.dev.yml down
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Build and start services (backend and frontend only, using host <PERSON><PERSON><PERSON>)
echo "🏗️  Building and starting services..."
docker-compose -f docker-compose.dev.yml up --build -d backend frontend

echo "⏳ Waiting for services to be healthy..."

# Wait for backend to be ready
echo "🐍 Waiting for Backend service..."
timeout=60
counter=0
while ! curl -s http://localhost:8000/health > /dev/null; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for Backend service"
        exit 1
    fi
    echo "   Backend not ready yet... (${counter}s/${timeout}s)"
    sleep 5
    counter=$((counter + 5))
done
echo "✅ Backend service is ready"

# Wait for frontend to be ready
echo "⚛️  Waiting for Frontend service..."
timeout=60
counter=0
while ! curl -s http://localhost:3000 > /dev/null; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for Frontend service"
        exit 1
    fi
    echo "   Frontend not ready yet... (${counter}s/${timeout}s)"
    sleep 5
    counter=$((counter + 5))
done
echo "✅ Frontend service is ready"

echo ""
echo "🎉 All services are running successfully!"
echo ""
echo "📖 Application URLs:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:8000"
echo "   API Docs:  http://localhost:8000/docs"
echo "   Ollama:    http://localhost:11434 (host)"
echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.dev.yml ps
echo ""
echo "📝 To view logs:"
echo "   docker-compose -f docker-compose.dev.yml logs -f"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose -f docker-compose.dev.yml down"
echo ""
echo "Press Ctrl+C to stop all services and exit"

# Keep script running and show logs
docker-compose -f docker-compose.dev.yml logs -f
